@page "/tables"
@using RestaurantApp.Data.Models

<PageTitle>Table Management</PageTitle>

<h1>Table Management</h1>

<div class="mb-3">
    <button class="btn btn-success" @onclick="ShowAddTableForm">Add New Table</button>
</div>

@if (showAddTableForm)
{
    <div class="card mb-3">
        <div class="card-header">
            <h5>Add New Table</h5>
        </div>
        <div class="card-body">
            <p><em>Add table form will be implemented here...</em></p>
            <button class="btn btn-secondary" @onclick="HideAddTableForm">Cancel</button>
        </div>
    </div>
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>Restaurant Tables</h5>
            </div>
            <div class="card-body">
                <p><em>Tables list will be displayed here...</em></p>
                <p>This page will show all tables with their capacity, location, and availability status.</p>
            </div>
        </div>
    </div>
</div>

@code {
    private bool showAddTableForm = false;

    private void ShowAddTableForm()
    {
        showAddTableForm = true;
    }

    private void HideAddTableForm()
    {
        showAddTableForm = false;
    }
}
