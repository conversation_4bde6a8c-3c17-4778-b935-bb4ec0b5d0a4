namespace RestaurantApp.Data.Models
{
    public class Order
    {
        public int Id { get; set; }
        public int TableId { get; set; }
        public Table? Table { get; set; }
        public DateTime OrderDate { get; set; } = DateTime.Now;
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public string Status { get; set; } = "Pending"; // Pending, Preparing, Ready, Served, Paid
        public List<OrderItem> OrderItems { get; set; } = new List<OrderItem>();
        public string Notes { get; set; } = string.Empty;
    }
}
