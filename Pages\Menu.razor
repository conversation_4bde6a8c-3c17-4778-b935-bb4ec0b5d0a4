@page "/menu"
@using RestaurantApp.Data.Models
@using RestaurantApp.Data.Services
@inject MenuService MenuService

<PageTitle>Menu Management</PageTitle>

<h1>Menu Management</h1>

<div class="mb-3">
    <button class="btn btn-success" @onclick="ShowAddForm">Add New Menu Item</button>
</div>

@if (showAddForm)
{
    <div class="card mb-3">
        <div class="card-header">
            <h5>Add New Menu Item</h5>
        </div>
        <div class="card-body">
            <p><em>Add menu item form will be implemented here...</em></p>
            <button class="btn btn-secondary" @onclick="HideAddForm">Cancel</button>
        </div>
    </div>
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>Menu Items</h5>
            </div>
            <div class="card-body">
                <p><em>Menu items list will be displayed here...</em></p>
                <p>This page will show all menu items with options to edit, delete, and manage availability.</p>
            </div>
        </div>
    </div>
</div>

@code {
    private bool showAddForm = false;

    private void ShowAddForm()
    {
        showAddForm = true;
    }

    private void HideAddForm()
    {
        showAddForm = false;
    }
}
