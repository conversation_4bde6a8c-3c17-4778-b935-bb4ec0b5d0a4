<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">Restaurant App</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="CollapseNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="oi oi-home" aria-hidden="true"></span> Home
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="menu">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Menu
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="orders">
                <span class="oi oi-clipboard" aria-hidden="true"></span> Orders
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="tables">
                <span class="oi oi-grid-three-up" aria-hidden="true"></span> Tables
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private void CollapseNavMenu()
    {
        collapseNavMenu = true;
    }
}
