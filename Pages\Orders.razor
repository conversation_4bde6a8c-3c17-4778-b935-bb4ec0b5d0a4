@page "/orders"
@using RestaurantApp.Data.Models
@using RestaurantApp.Data.Services
@inject OrderService OrderService

<PageTitle>Order Management</PageTitle>

<h1>Order Management</h1>

<div class="mb-3">
    <button class="btn btn-success" @onclick="ShowNewOrderForm">Create New Order</button>
</div>

@if (showNewOrderForm)
{
    <div class="card mb-3">
        <div class="card-header">
            <h5>Create New Order</h5>
        </div>
        <div class="card-body">
            <p><em>New order form will be implemented here...</em></p>
            <button class="btn btn-secondary" @onclick="HideNewOrderForm">Cancel</button>
        </div>
    </div>
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>Current Orders</h5>
            </div>
            <div class="card-body">
                <p><em>Orders list will be displayed here...</em></p>
                <p>This page will show all orders with their status, table information, and order details.</p>
            </div>
        </div>
    </div>
</div>

@code {
    private bool showNewOrderForm = false;

    private void ShowNewOrderForm()
    {
        showNewOrderForm = true;
    }

    private void HideNewOrderForm()
    {
        showNewOrderForm = false;
    }
}
